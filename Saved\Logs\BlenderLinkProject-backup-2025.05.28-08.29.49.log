﻿Log file open, 05/28/25 13:57:17
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=33480)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.243216
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-07689C5C40C41E594100B091C6C41AE1
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.05 seconds
LogAssetRegistry: Display: Asset registry cache read as 43.8 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.57ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.28-08.27.18:396][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.28-08.27.18:396][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.28-08.27.18:398][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.28-08.27.18:398][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.28-08.27.18:398][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.28-08.27.18:399][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.28-08.27.18:399][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.28-08.27.18:401][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-08.27.18:401][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-08.27.18:401][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-08.27.18:405][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.28-08.27.18:405][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-08.27.18:497][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.28-08.27.18:497][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-08.27.18:497][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.28-08.27.18:497][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-08.27.18:497][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.28-08.27.18:666][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.28-08.27.18:666][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-08.27.18:666][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-08.27.18:666][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.28-08.27.18:666][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.28-08.27.18:674][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.28-08.27.18:674][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.28-08.27.18:674][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-08.27.18:674][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.28-08.27.18:674][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.28-08.27.18:675][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-08.27.18:675][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.28-08.27.18:675][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.28-08.27.18:675][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.28-08.27.18:675][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.27.18:675][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.28-08.27.18:675][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.28-08.27.18:675][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-08.27.18:675][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.28-08.27.18:675][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.28-08.27.18:675][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.28-08.27.18:675][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.28-08.27.18:675][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.28-08.27.18:675][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-08.27.18:675][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.28-08.27.18:675][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.28-08.27.18:675][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.28-08.27.18:675][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.28-08.27.18:675][  0]LogInit: User: Shashank
[2025.05.28-08.27.18:675][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.28-08.27.18:675][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.28-08.27.18:947][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.28-08.27.18:947][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.28-08.27.18:947][  0]LogMemory: Process Physical Memory: 629.18 MB used, 647.65 MB peak
[2025.05.28-08.27.18:947][  0]LogMemory: Process Virtual Memory: 760.29 MB used, 760.29 MB peak
[2025.05.28-08.27.18:947][  0]LogMemory: Physical Memory: 23394.50 MB used,  42058.90 MB free, 65453.40 MB total
[2025.05.28-08.27.18:947][  0]LogMemory: Virtual Memory: 38874.83 MB used,  30674.57 MB free, 69549.40 MB total
[2025.05.28-08.27.18:947][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.28-08.27.18:951][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.28-08.27.18:957][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.28-08.27.18:957][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.28-08.27.18:959][  0]LogInit: Using OS detected language (en-GB).
[2025.05.28-08.27.18:959][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.28-08.27.18:961][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.28-08.27.18:961][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.28-08.27.19:205][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.28-08.27.19:205][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.28-08.27.19:206][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-08.27.19:217][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.28-08.27.19:217][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.28-08.27.19:300][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-08.27.19:300][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-08.27.19:300][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-08.27.19:300][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-08.27.19:300][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-08.27.19:300][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.28-08.27.19:301][  0]LogWindows: Attached monitors:
[2025.05.28-08.27.19:301][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-08.27.19:301][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-08.27.19:301][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-08.27.19:301][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-08.27.19:301][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-08.27.19:301][  0]LogRHI: RHI Adapter Info:
[2025.05.28-08.27.19:301][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.28-08.27.19:301][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-08.27.19:301][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.28-08.27.19:301][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.28-08.27.19:325][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.28-08.27.19:387][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.28-08.27.19:387][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.28-08.27.19:461][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Raster order views are supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.28-08.27.19:461][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000734654B5300)
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000734654B5580)
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000734654B5800)
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.28-08.27.19:487][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.28-08.27.19:487][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.28-08.27.19:498][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.28-08.27.19:501][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.28-08.27.19:508][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.28-08.27.19:508][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.28-08.27.19:525][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.28-08.27.19:525][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.28-08.27.19:525][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.28-08.27.19:525][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.28-08.27.19:525][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.28-08.27.19:525][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.28-08.27.19:525][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.28-08.27.19:526][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.28-08.27.19:526][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.28-08.27.19:548][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.28-08.27.19:564][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.28-08.27.19:564][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.28-08.27.19:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.28-08.27.19:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.28-08.27.19:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.28-08.27.19:577][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.28-08.27.19:590][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.28-08.27.19:590][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.28-08.27.19:590][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.28-08.27.19:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.28-08.27.19:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.28-08.27.19:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.28-08.27.19:603][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.28-08.27.19:615][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.28-08.27.19:615][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.28-08.27.19:630][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.28-08.27.19:630][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.28-08.27.19:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.28-08.27.19:631][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.28-08.27.19:631][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.28-08.27.19:669][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.28-08.27.19:671][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.28-08.27.19:671][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.28-08.27.19:671][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.28-08.27.19:674][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.28-08.27.19:674][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.28-08.27.19:674][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.28-08.27.19:674][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-08.27.19:674][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.28-08.27.19:731][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.28-08.27.19:731][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-08.27.19:731][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.28-08.27.19:732][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.28-08.27.19:732][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-08.27.19:733][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-08.27.19:733][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.28-08.27.19:733][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 33544 --child-id Zen_33544_Startup'
[2025.05.28-08.27.19:798][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.28-08.27.19:798][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.066 seconds
[2025.05.28-08.27.19:800][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.28-08.27.19:803][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.28-08.27.19:803][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=2297.79MBs, RandomWriteSpeed=318.89MBs. Assigned SpeedClass 'Local'
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.28-08.27.19:804][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.28-08.27.19:804][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/33544/).
[2025.05.28-08.27.19:805][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/167F7ED441BEF31C81900DA2346C312E/'.
[2025.05.28-08.27.19:805][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.28-08.27.19:805][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.28-08.27.19:806][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.28-08.27.19:806][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.28-08.27.20:206][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.28-08.27.20:750][  0]LogSlate: Using FreeType 2.10.0
[2025.05.28-08.27.20:751][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.28-08.27.20:751][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.27.20:751][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.27.20:751][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.27.20:751][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.27.20:751][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.27.20:751][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.27.20:775][  0]LogAssetRegistry: FAssetRegistry took 0.0019 seconds to start up
[2025.05.28-08.27.20:776][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.28-08.27.20:781][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.28-08.27.20:781][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.28-08.27.20:957][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.27.20:959][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.28-08.27.20:959][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.28-08.27.20:959][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.28-08.27.20:970][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.28-08.27.20:970][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.28-08.27.20:992][  0]LogDeviceProfileManager: Active device profile: [00000734825CEE00][0000073480740000 66] WindowsEditor
[2025.05.28-08.27.20:993][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.28-08.27.20:993][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.28-08.27.20:996][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.28-08.27.20:996][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:026][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.27.21:027][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:028][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-08.27.21:029][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.28-08.27.21:030][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.27.21:176][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.28-08.27.21:176][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.28-08.27.21:176][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.28-08.27.21:176][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.28-08.27.21:176][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.28-08.27.21:301][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-08.27.21:319][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.52ms
[2025.05.28-08.27.21:330][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-08.27.21:331][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.50ms
[2025.05.28-08.27.21:511][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.28-08.27.21:511][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.28-08.27.21:516][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.28-08.27.21:516][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.28-08.27.21:516][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.28-08.27.21:519][  0]LogLiveCoding: Display: Waiting for server
[2025.05.28-08.27.21:531][  0]LogSlate: Border
[2025.05.28-08.27.21:531][  0]LogSlate: BreadcrumbButton
[2025.05.28-08.27.21:531][  0]LogSlate: Brushes.Title
[2025.05.28-08.27.21:531][  0]LogSlate: Default
[2025.05.28-08.27.21:531][  0]LogSlate: Icons.Save
[2025.05.28-08.27.21:531][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.28-08.27.21:531][  0]LogSlate: ListView
[2025.05.28-08.27.21:531][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.28-08.27.21:531][  0]LogSlate: SoftwareCursor_Grab
[2025.05.28-08.27.21:531][  0]LogSlate: TableView.DarkRow
[2025.05.28-08.27.21:531][  0]LogSlate: TableView.Row
[2025.05.28-08.27.21:531][  0]LogSlate: TreeView
[2025.05.28-08.27.21:592][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.28-08.27.21:596][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.28-08.27.21:598][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.869 ms
[2025.05.28-08.27.21:606][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.49ms
[2025.05.28-08.27.21:620][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.28-08.27.21:620][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.28-08.27.21:620][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.28-08.27.21:620][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.28-08.27.21:673][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 3DB04808F3554C988000000000008C00 | Instance: C8A631EC4F994BA5864A36BC08AAE033 (DESKTOP-E41IK6R-33544).
[2025.05.28-08.27.21:811][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.28-08.27.21:814][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.28-08.27.21:814][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.28-08.27.21:815][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:51787'.
[2025.05.28-08.27.21:817][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.05.28-08.27.21:817][  0]LogUdpMessaging: Display: Added local interface '172.27.16.1' to multicast group '230.0.0.1:6666'
[2025.05.28-08.27.21:933][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.28-08.27.21:933][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.28-08.27.21:933][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.28-08.27.21:933][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.28-08.27.21:933][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.28-08.27.22:032][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.28-08.27.22:032][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.28-08.27.22:046][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.28-08.27.22:194][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-08.27.22:194][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-08.27.22:220][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-08.27.22:269][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.28-08.27.22:486][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.28-08.27.22:565][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.28-08.27.22:996][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.28-08.27.23:079][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.28-08.27.29:471][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.29:484][  0]LogSkeletalMesh: Built Skeletal Mesh [6.49s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.28-08.27.29:520][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.27.29:521][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.27.29:521][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.27.29:521][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.27.29:521][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.27.29:521][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.27.29:574][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.28-08.27.29:581][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.28-08.27.29:592][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-08.27.29:592][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-08.27.29:665][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.28-08.27.29:665][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.28-08.27.29:665][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.28-08.27.29:665][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.28-08.27.29:666][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.28-08.27.29:666][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.28-08.27.29:666][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.28-08.27.29:666][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.28-08.27.29:666][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.28-08.27.29:667][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.28-08.27.29:667][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.28-08.27.29:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.28-08.27.29:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.28-08.27.29:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.28-08.27.29:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.28-08.27.29:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.28-08.27.29:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.28-08.27.29:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.28-08.27.29:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.28-08.27.29:670][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.28-08.27.29:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.28-08.27.29:671][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.28-08.27.29:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.28-08.27.29:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.28-08.27.29:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.28-08.27.29:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.28-08.27.29:672][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.28-08.27.29:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.28-08.27.29:673][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.28-08.27.29:767][  0]SourceControl: Revision control is disabled
[2025.05.28-08.27.29:778][  0]SourceControl: Revision control is disabled
[2025.05.28-08.27.29:799][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.51ms
[2025.05.28-08.27.29:808][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.47ms
[2025.05.28-08.27.30:014][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-08.27.30:014][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-08.27.30:042][  0]LogCollectionManager: Loaded 0 collections in 0.000737 seconds
[2025.05.28-08.27.30:044][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.28-08.27.30:044][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.28-08.27.30:046][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.28-08.27.30:096][  0]LogBlenderLink: Waiting for client connection...
[2025.05.28-08.27.30:112][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.27.30:113][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.27.30:113][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.27.30:113][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.27.30:113][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.27.30:113][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.27.30:126][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-08.27.30:126][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-08.27.30:140][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-28T08:27:30.140Z using C
[2025.05.28-08.27.30:140][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.28-08.27.30:140][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-08.27.30:140][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.28-08.27.30:145][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.28-08.27.30:146][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.28-08.27.30:146][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.28-08.27.30:146][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000051
[2025.05.28-08.27.30:146][  0]LogFab: Display: Logging in using persist
[2025.05.28-08.27.30:146][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.28-08.27.30:176][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.05.28-08.27.30:176][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.28-08.27.30:188][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.28-08.27.30:188][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.28-08.27.30:298][  0]LogEngine: Initializing Engine...
[2025.05.28-08.27.30:301][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.28-08.27.30:301][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.28-08.27.30:374][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.28-08.27.30:388][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.28-08.27.30:397][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.28-08.27.30:397][  0]LogInit: Texture streaming: Enabled
[2025.05.28-08.27.30:405][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.28-08.27.30:416][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.28-08.27.30:421][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.28-08.27.30:421][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.28-08.27.30:422][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.28-08.27.30:422][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.28-08.27.30:422][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.28-08.27.30:422][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.28-08.27.30:422][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.28-08.27.30:422][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.28-08.27.30:422][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.28-08.27.30:427][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.28-08.27.30:485][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.28-08.27.30:486][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.28-08.27.30:486][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.28-08.27.30:486][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.28-08.27.30:487][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.28-08.27.30:487][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.28-08.27.30:489][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.28-08.27.30:489][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.28-08.27.30:489][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.28-08.27.30:490][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.28-08.27.30:490][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.28-08.27.30:495][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.28-08.27.30:498][  0]LogInit: Undo buffer set to 256 MB
[2025.05.28-08.27.30:498][  0]LogInit: Transaction tracking system initialized
[2025.05.28-08.27.30:509][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.28-08.27.30:562][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.60ms
[2025.05.28-08.27.30:564][  0]LocalizationService: Localization service is disabled
[2025.05.28-08.27.30:578][  0]LogTimingProfiler: Initialize
[2025.05.28-08.27.30:578][  0]LogTimingProfiler: OnSessionChanged
[2025.05.28-08.27.30:578][  0]LoadingProfiler: Initialize
[2025.05.28-08.27.30:578][  0]LoadingProfiler: OnSessionChanged
[2025.05.28-08.27.30:578][  0]LogNetworkingProfiler: Initialize
[2025.05.28-08.27.30:578][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.28-08.27.30:578][  0]LogMemoryProfiler: Initialize
[2025.05.28-08.27.30:578][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.28-08.27.30:708][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.28-08.27.30:718][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.28-08.27.30:742][  0]LogPython: Using Python 3.11.8
[2025.05.28-08.27.31:748][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.28-08.27.31:769][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.28-08.27.31:830][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.28-08.27.31:830][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.28-08.27.31:866][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.28-08.27.31:886][  0]LogEditorDataStorage: Initializing
[2025.05.28-08.27.31:888][  0]LogEditorDataStorage: Initialized
[2025.05.28-08.27.31:889][  0]LogWindows: Attached monitors:
[2025.05.28-08.27.31:889][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-08.27.31:889][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-08.27.31:889][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-08.27.31:889][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-08.27.31:889][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-08.27.31:893][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.28-08.27.31:906][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.28-08.27.31:908][  0]SourceControl: Revision control is disabled
[2025.05.28-08.27.31:908][  0]LogUnrealEdMisc: Loading editor; pre map load, took 14.245
[2025.05.28-08.27.31:909][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.28-08.27.31:911][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.27.31:911][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.27.31:959][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.28-08.27.31:960][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.27.31:967][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.28-08.27.31:967][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.28-08.27.31:970][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.28-08.27.31:970][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.28-08.27.31:970][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.28-08.27.32:672][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.28-08.27.34:952][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.27.34:956][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.27.34:957][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.27.34:959][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.28-08.27.34:959][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.28-08.27.34:959][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.27.34:960][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.28-08.27.36:921][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.28-08.27.36:963][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.28-08.27.37:337][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.37:341][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.28-08.27.37:353][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.28-08.27.37:354][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.28-08.27.37:723][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.37:724][  0]LogSkeletalMesh: Built Skeletal Mesh [0.37s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.28-08.27.38:848][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.38:852][  0]LogSkeletalMesh: Built Skeletal Mesh [1.50s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.28-08.27.38:922][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.28-08.27.39:138][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.39:142][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.28-08.27.39:143][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.28-08.27.39:451][  0]LogWorldPartition: Display: WorldPartition initialize took 7.48 sec
[2025.05.28-08.27.39:526][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.28-08.27.44:569][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.27.44:584][  0]LogSkeletalMesh: Built Skeletal Mesh [5.44s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-08.27.45:266][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.28-08.27.45:496][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.27ms
[2025.05.28-08.27.45:497][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.28-08.27.45:499][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.189ms to complete.
[2025.05.28-08.27.45:507][  0]LogUnrealEdMisc: Total Editor Startup Time, took 27.844
[2025.05.28-08.27.45:686][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.28-08.27.45:783][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.27.45:839][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.27.45:907][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.27.45:960][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.27.45:989][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:990][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.28-08.27.45:990][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:990][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.28-08.27.45:990][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:990][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.28-08.27.45:990][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:990][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.28-08.27.45:991][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:991][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.28-08.27.45:991][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:992][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.28-08.27.45:992][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:992][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.28-08.27.45:992][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:992][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.28-08.27.45:992][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:992][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.28-08.27.45:992][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.27.45:992][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.28-08.27.46:037][  0]LogSlate: Took 0.000171 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.28-08.27.46:266][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.05.28-08.27.46:267][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.05.28-08.27.46:361][  0]LogSlate: Took 0.000132 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.28-08.27.46:364][  0]LogSlate: Took 0.000103 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.28-08.27.46:366][  0]LogSlate: Took 0.000095 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.28-08.27.46:396][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.28-08.27.46:397][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.28-08.27.46:397][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.28-08.27.46:397][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-08.27.46:463][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-08.27.46:463][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.28-08.27.46:463][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.28-08.27.46:464][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.28-08.27.46:464][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-08.27.46:530][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-08.27.46:530][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.28-08.27.46:565][  0]LogSlate: Took 0.000904 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-08.27.46:867][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 29.63 ms. Compile time 16.10 ms, link time 13.12 ms.
[2025.05.28-08.27.47:063][  0]LogStall: Startup...
[2025.05.28-08.27.47:066][  0]LogStall: Startup complete.
[2025.05.28-08.27.47:073][  0]LogLoad: (Engine Initialization) Total time: 29.41 seconds
[2025.05.28-08.27.47:321][  0]LogAssetRegistry: AssetRegistryGather time 0.0895s: AssetDataDiscovery 0.0148s, AssetDataGather 0.0179s, StoreResults 0.0567s. Wall time 26.5480s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 7964. NumUncachedFiles 128.
	BackgroundTickInterruptions 10.
[2025.05.28-08.27.47:341][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.28-08.27.47:341][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.28-08.27.47:469][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.127763 seconds (Found 8068 uncontrolled assets)
[2025.05.28-08.27.47:528][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.28-08.27.47:528][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.28-08.27.47:716][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.27.47:718][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.28-08.27.47:718][  0]LogFab: Display: Logging in using exchange code
[2025.05.28-08.27.47:718][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.28-08.27.47:718][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.28-08.27.47:719][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.28-08.27.47:745][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.28-08.27.47:749][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 29.925 ms
[2025.05.28-08.27.47:763][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.28-08.27.48:017][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.28-08.27.48:763][ 14]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 18.569235
[2025.05.28-08.27.48:764][ 14]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.28-08.27.48:765][ 14]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18.617722
[2025.05.28-08.27.49:177][ 21]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-08.27.49:710][ 31]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 19.514359
[2025.05.28-08.27.49:712][ 31]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.28-08.27.49:712][ 31]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19.514359, Update Interval: 326.459534
[2025.05.28-08.27.57:108][332]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.07:115][260]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.17:125][231]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.27:205][ 80]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.37:348][883]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.42:218][954]Cmd: Interchange.FeatureFlags.Import.FBX False
[2025.05.28-08.28.42:218][954]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.28-08.28.46:481][348]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.46:481][348]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.46:481][348]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.46:554][348]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.28-08.28.46:562][348]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.28-08.28.46:577][348]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.46:578][348]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.46:590][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F87800 with pending SubmitJob call.
[2025.05.28-08.28.46:590][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F86400 with pending SubmitJob call.
[2025.05.28-08.28.46:591][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD3200 with pending SubmitJob call.
[2025.05.28-08.28.46:592][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD6E00 with pending SubmitJob call.
[2025.05.28-08.28.46:592][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD5A00 with pending SubmitJob call.
[2025.05.28-08.28.46:592][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD8200 with pending SubmitJob call.
[2025.05.28-08.28.46:594][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F83200 with pending SubmitJob call.
[2025.05.28-08.28.46:595][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F85A00 with pending SubmitJob call.
[2025.05.28-08.28.46:595][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB7800 with pending SubmitJob call.
[2025.05.28-08.28.46:595][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB0000 with pending SubmitJob call.
[2025.05.28-08.28.46:595][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB2800 with pending SubmitJob call.
[2025.05.28-08.28.46:596][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F88C00 with pending SubmitJob call.
[2025.05.28-08.28.46:596][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB1400 with pending SubmitJob call.
[2025.05.28-08.28.46:596][348]LogShaderCompilers: Display: Cancelled job 0x0000073590FE3C00 with pending SubmitJob call.
[2025.05.28-08.28.46:596][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB5A00 with pending SubmitJob call.
[2025.05.28-08.28.46:596][348]LogShaderCompilers: Display: Cancelled job 0x0000073590FE6400 with pending SubmitJob call.
[2025.05.28-08.28.46:597][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB4600 with pending SubmitJob call.
[2025.05.28-08.28.46:598][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F86E00 with pending SubmitJob call.
[2025.05.28-08.28.46:598][348]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD1E00 with pending SubmitJob call.
[2025.05.28-08.28.46:599][348]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.28-08.28.46:599][348]LogShaderCompilers: Display: Cancelled job 0x0000073596F83C00 with pending SubmitJob call.
[2025.05.28-08.28.46:613][348]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.46:620][348]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.28-08.28.46:622][348]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.28.46:628][348]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.28.46:629][348]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.28-08.28.46:636][348]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.46:636][348]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.46:636][348]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.46:637][348]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.28-08.28.46:646][348]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.46:646][348]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.46:646][348]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.46:737][348]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-08.28.46:772][348]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.28.46:790][348]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.28.46:805][348]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.46:834][348]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.28.46:850][348]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (cartilage_lod0_mesh) ...
[2025.05.28-08.28.46:850][348]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.28.46:853][348]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.46:853][348]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.46:932][349]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.28.47:054][353]LogStreaming: Display: FlushAsyncLoading(522): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.47:054][353]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.47:054][353]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.47:256][353]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.28-08.28.47:261][353]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.28-08.28.47:264][353]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.47:265][353]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.47:277][353]LogShaderCompilers: Display: Cancelled job 0x0000073590FE1400 with pending SubmitJob call.
[2025.05.28-08.28.47:277][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB3C00 with pending SubmitJob call.
[2025.05.28-08.28.47:277][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F86400 with pending SubmitJob call.
[2025.05.28-08.28.47:278][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB7800 with pending SubmitJob call.
[2025.05.28-08.28.47:278][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CBE600 with pending SubmitJob call.
[2025.05.28-08.28.47:278][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CBA000 with pending SubmitJob call.
[2025.05.28-08.28.47:278][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB9600 with pending SubmitJob call.
[2025.05.28-08.28.47:280][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB5A00 with pending SubmitJob call.
[2025.05.28-08.28.47:280][353]LogShaderCompilers: Display: Cancelled job 0x0000073590FE1E00 with pending SubmitJob call.
[2025.05.28-08.28.47:281][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F83200 with pending SubmitJob call.
[2025.05.28-08.28.47:281][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB8C00 with pending SubmitJob call.
[2025.05.28-08.28.47:281][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F85000 with pending SubmitJob call.
[2025.05.28-08.28.47:282][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CBB400 with pending SubmitJob call.
[2025.05.28-08.28.47:282][353]LogShaderCompilers: Display: Cancelled job 0x0000073559602800 with pending SubmitJob call.
[2025.05.28-08.28.47:282][353]LogShaderCompilers: Display: Cancelled job 0x0000073590FE8200 with pending SubmitJob call.
[2025.05.28-08.28.47:282][353]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.28-08.28.47:283][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F81400 with pending SubmitJob call.
[2025.05.28-08.28.47:283][353]LogShaderCompilers: Display: Cancelled job 0x0000073559606400 with pending SubmitJob call.
[2025.05.28-08.28.47:284][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F87800 with pending SubmitJob call.
[2025.05.28-08.28.47:284][353]LogShaderCompilers: Display: Cancelled job 0x0000073596F84600 with pending SubmitJob call.
[2025.05.28-08.28.47:285][353]LogShaderCompilers: Display: Cancelled job 0x00000735A7CB5000 with pending SubmitJob call.
[2025.05.28-08.28.47:289][353]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.47:296][353]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.28-08.28.47:296][353]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.28-08.28.47:300][353]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.28-08.28.47:301][353]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.28-08.28.47:308][353]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.47:309][353]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.47:309][353]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.47:309][353]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.28-08.28.47:316][353]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.47:316][353]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.47:316][353]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.47:332][353]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.28.47:364][353]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-08.28.47:382][353]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.28.47:398][353]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.47:400][353]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.47:401][353]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.47:424][354]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-08.28.47:431][354]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.28.47:490][357]LogStreaming: Display: FlushAsyncLoading(528): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.47:491][357]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.47:491][357]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.47:709][357]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.28-08.28.47:714][357]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.28-08.28.47:718][357]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.47:720][357]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.47:778][357]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.28.47:891][357]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.28-08.28.47:914][357]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.47:940][357]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.28.47:941][357]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.28.47:958][357]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.28.47:963][357]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.28.47:964][357]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.28-08.28.47:971][357]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.47:971][357]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.47:972][357]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.47:972][357]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.28-08.28.47:979][357]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.47:979][357]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.47:979][357]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.47:995][357]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.28.48:027][357]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.28.48:044][357]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-08.28.48:060][357]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.48:068][357]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.28.48:083][357]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.28.48:099][357]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.28.48:100][357]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.48:100][357]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.48:123][358]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-08.28.48:248][362]LogStreaming: Display: FlushAsyncLoading(536): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.48:248][362]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.48:248][362]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.48:450][362]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.28-08.28.48:455][362]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.28-08.28.48:459][362]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.48:461][362]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.48:485][362]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.28-08.28.48:522][362]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.28.48:640][362]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.28-08.28.48:662][362]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.48:688][362]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.28.48:688][362]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.28.48:704][362]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.28.48:710][362]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.28.48:711][362]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.28-08.28.48:719][362]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.48:719][362]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.48:719][362]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.48:720][362]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.28-08.28.48:725][362]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.48:725][362]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.48:725][362]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.48:743][362]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.28.48:775][362]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.05.28-08.28.48:792][362]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.05.28-08.28.48:808][362]LogUObjectHash: Compacting FUObjectHashTables data took   0.39ms
[2025.05.28-08.28.48:815][362]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.28.48:831][362]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.28.48:847][362]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.28.48:848][362]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.48:848][362]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.48:870][363]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-08.28.48:940][366]LogStreaming: Display: FlushAsyncLoading(542): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.48:940][366]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.48:940][366]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.49:149][366]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.28-08.28.49:154][366]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.28-08.28.49:158][366]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.49:159][366]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.49:171][366]LogShaderCompilers: Display: Cancelled job 0x0000073559600A00 with pending SubmitJob call.
[2025.05.28-08.28.49:171][366]LogShaderCompilers: Display: Cancelled job 0x0000073559609600 with pending SubmitJob call.
[2025.05.28-08.28.49:171][366]LogShaderCompilers: Display: Cancelled job 0x000007358DF98C00 with pending SubmitJob call.
[2025.05.28-08.28.49:171][366]LogShaderCompilers: Display: Cancelled job 0x0000073559604600 with pending SubmitJob call.
[2025.05.28-08.28.49:171][366]LogShaderCompilers: Display: Cancelled job 0x000007358DF92800 with pending SubmitJob call.
[2025.05.28-08.28.49:172][366]LogShaderCompilers: Display: Cancelled job 0x000007358DF93C00 with pending SubmitJob call.
[2025.05.28-08.28.49:174][366]LogShaderCompilers: Display: Cancelled job 0x000007358DF94600 with pending SubmitJob call.
[2025.05.28-08.28.49:174][366]LogShaderCompilers: Display: Cancelled job 0x0000073559606400 with pending SubmitJob call.
[2025.05.28-08.28.49:175][366]LogShaderCompilers: Display: Cancelled job 0x000007355810E600 with pending SubmitJob call.
[2025.05.28-08.28.49:175][366]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.28-08.28.49:175][366]LogShaderCompilers: Display: Cancelled job 0x0000073493A2E600 with pending SubmitJob call.
[2025.05.28-08.28.49:175][366]LogShaderCompilers: Display: Cancelled job 0x0000073558108C00 with pending SubmitJob call.
[2025.05.28-08.28.49:176][366]LogShaderCompilers: Display: Cancelled job 0x0000073493A28C00 with pending SubmitJob call.
[2025.05.28-08.28.49:176][366]LogShaderCompilers: Display: Cancelled job 0x000007355810DC00 with pending SubmitJob call.
[2025.05.28-08.28.49:176][366]LogShaderCompilers: Display: Cancelled job 0x0000073559605A00 with pending SubmitJob call.
[2025.05.28-08.28.49:177][366]LogShaderCompilers: Display: Cancelled job 0x000007355810D200 with pending SubmitJob call.
[2025.05.28-08.28.49:177][366]LogShaderCompilers: Display: Cancelled job 0x00000734E0753C00 with pending SubmitJob call.
[2025.05.28-08.28.49:178][366]LogShaderCompilers: Display: Cancelled job 0x00000734E0751400 with pending SubmitJob call.
[2025.05.28-08.28.49:179][366]LogShaderCompilers: Display: Cancelled job 0x000007355810C800 with pending SubmitJob call.
[2025.05.28-08.28.49:179][366]LogShaderCompilers: Display: Cancelled job 0x0000073559608C00 with pending SubmitJob call.
[2025.05.28-08.28.49:180][366]LogShaderCompilers: Display: Cancelled job 0x000007358DF90A00 with pending SubmitJob call.
[2025.05.28-08.28.49:203][366]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.49:218][366]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.28-08.28.49:219][366]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.28-08.28.49:230][366]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.28-08.28.49:231][366]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.28-08.28.49:238][366]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.49:238][366]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.49:238][366]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.49:239][366]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.28-08.28.49:245][366]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.49:245][366]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.49:245][366]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.49:261][366]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.28.49:291][366]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.28.49:309][366]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-08.28.49:325][366]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.49:328][366]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.49:328][366]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.49:351][367]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.28.49:379][369]LogStreaming: Display: FlushAsyncLoading(548): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.49:379][369]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.49:379][369]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.49:588][369]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.28-08.28.49:593][369]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.28-08.28.49:596][369]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.49:597][369]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.49:611][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB5A00 with pending SubmitJob call.
[2025.05.28-08.28.49:612][369]LogShaderCompilers: Display: Cancelled job 0x0000073504841400 with pending SubmitJob call.
[2025.05.28-08.28.49:612][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD0A00 with pending SubmitJob call.
[2025.05.28-08.28.49:614][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BBA000 with pending SubmitJob call.
[2025.05.28-08.28.49:614][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB3200 with pending SubmitJob call.
[2025.05.28-08.28.49:614][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD5A00 with pending SubmitJob call.
[2025.05.28-08.28.49:615][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BBDC00 with pending SubmitJob call.
[2025.05.28-08.28.49:616][369]LogShaderCompilers: Display: Cancelled job 0x0000073590FE1E00 with pending SubmitJob call.
[2025.05.28-08.28.49:616][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD9600 with pending SubmitJob call.
[2025.05.28-08.28.49:616][369]LogShaderCompilers: Display: Cancelled job 0x0000073557E16400 with pending SubmitJob call.
[2025.05.28-08.28.49:616][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6AD3200 with pending SubmitJob call.
[2025.05.28-08.28.49:616][369]LogShaderCompilers: Display: Cancelled job 0x0000073590FE5000 with pending SubmitJob call.
[2025.05.28-08.28.49:617][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB4600 with pending SubmitJob call.
[2025.05.28-08.28.49:617][369]LogShaderCompilers: Display: Cancelled job 0x0000073590FE1400 with pending SubmitJob call.
[2025.05.28-08.28.49:618][369]LogShaderCompilers: Display: Cancelled job 0x00000735A7CBE600 with pending SubmitJob call.
[2025.05.28-08.28.49:618][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB0A00 with pending SubmitJob call.
[2025.05.28-08.28.49:619][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BB9600 with pending SubmitJob call.
[2025.05.28-08.28.49:619][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BBB400 with pending SubmitJob call.
[2025.05.28-08.28.49:619][369]LogShaderCompilers: Display: Cancelled job 0x00000735A7CBAA00 with pending SubmitJob call.
[2025.05.28-08.28.49:619][369]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.28-08.28.49:619][369]LogShaderCompilers: Display: Cancelled job 0x00000735A6BBD200 with pending SubmitJob call.
[2025.05.28-08.28.49:638][369]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.49:651][369]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.28-08.28.49:652][369]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.28-08.28.49:663][369]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.28-08.28.49:665][369]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.28-08.28.49:672][369]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.49:721][369]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.49:721][369]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.49:722][369]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.28-08.28.49:728][369]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.49:728][369]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.49:728][369]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.49:746][369]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.28.49:777][369]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.28.49:794][369]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.28.49:809][369]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.49:813][369]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.28.49:813][369]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.28.49:837][370]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-08.28.49:961][374]LogStreaming: Display: FlushAsyncLoading(554): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.28.49:961][374]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.28-08.28.49:962][374]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.28-08.28.50:230][374]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.28-08.28.50:299][374]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.28-08.28.53:567][374]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.28.53:571][374]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.28.53:639][374]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.28.53:754][374]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.28-08.28.55:074][374]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.28.55:294][374]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.28.55:787][374]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.28-08.28.55:855][374]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.28.55:872][374]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.28.55:920][374]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.28.56:418][374]LogSkeletalMesh: Built Skeletal Mesh [0.56s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.28.56:424][374]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.28-08.28.56:434][374]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.56:434][374]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.56:434][374]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.56:437][374]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.28-08.28.56:444][374]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.28.56:444][374]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.28.56:444][374]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.28.56:461][374]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.28.56:492][374]LogUObjectHash: Compacting FUObjectHashTables data took   0.66ms
[2025.05.28-08.28.56:509][374]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.05.28-08.28.56:526][374]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.28.59:629][374]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.28.59:645][374]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.28.59:894][374]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.29.03:643][374]LogSkeletalMesh: Built Skeletal Mesh [4.01s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.29.03:756][374]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.29.03:756][374]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.29.03:782][375]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.29.03:830][375]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-08.29.03:838][375]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.29.03:903][378]LogStreaming: Display: FlushAsyncLoading(562): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.29.03:903][378]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.28-08.29.03:903][378]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.28-08.29.04:128][378]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.28-08.29.04:132][378]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.28-08.29.04:136][378]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.29.04:137][378]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.29.04:149][378]LogShaderCompilers: Display: Cancelled job 0x000007358662D200 with pending SubmitJob call.
[2025.05.28-08.29.04:150][378]LogShaderCompilers: Display: Cancelled job 0x0000073586628200 with pending SubmitJob call.
[2025.05.28-08.29.04:150][378]LogShaderCompilers: Display: Cancelled job 0x0000073586625000 with pending SubmitJob call.
[2025.05.28-08.29.04:150][378]LogShaderCompilers: Display: Cancelled job 0x0000073586625A00 with pending SubmitJob call.
[2025.05.28-08.29.04:150][378]LogShaderCompilers: Display: Cancelled job 0x0000073586627800 with pending SubmitJob call.
[2025.05.28-08.29.04:152][378]LogShaderCompilers: Display: Cancelled job 0x0000073586621400 with pending SubmitJob call.
[2025.05.28-08.29.04:152][378]LogShaderCompilers: Display: Cancelled job 0x000007358662A000 with pending SubmitJob call.
[2025.05.28-08.29.04:153][378]LogShaderCompilers: Display: Cancelled job 0x000007355D460000 with pending SubmitJob call.
[2025.05.28-08.29.04:153][378]LogShaderCompilers: Display: Cancelled job 0x0000073597742800 with pending SubmitJob call.
[2025.05.28-08.29.04:154][378]LogShaderCompilers: Display: Cancelled job 0x000007358664A000 with pending SubmitJob call.
[2025.05.28-08.29.04:154][378]LogShaderCompilers: Display: Cancelled job 0x000007358662AA00 with pending SubmitJob call.
[2025.05.28-08.29.04:154][378]LogShaderCompilers: Display: Cancelled job 0x000007355D460A00 with pending SubmitJob call.
[2025.05.28-08.29.04:155][378]LogShaderCompilers: Display: Cancelled job 0x000007358662E600 with pending SubmitJob call.
[2025.05.28-08.29.04:155][378]LogShaderCompilers: Display: Cancelled job 0x0000073586623C00 with pending SubmitJob call.
[2025.05.28-08.29.04:155][378]LogShaderCompilers: Display: Cancelled job 0x0000073586621E00 with pending SubmitJob call.
[2025.05.28-08.29.04:156][378]LogShaderCompilers: Display: Cancelled job 0x000007355D462800 with pending SubmitJob call.
[2025.05.28-08.29.04:156][378]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.28-08.29.04:157][378]LogShaderCompilers: Display: Cancelled job 0x000007358662F000 with pending SubmitJob call.
[2025.05.28-08.29.04:157][378]LogShaderCompilers: Display: Cancelled job 0x00000735A5F10A00 with pending SubmitJob call.
[2025.05.28-08.29.04:157][378]LogShaderCompilers: Display: Cancelled job 0x000007358662B400 with pending SubmitJob call.
[2025.05.28-08.29.04:158][378]LogShaderCompilers: Display: Cancelled job 0x00000735A7BB6400 with pending SubmitJob call.
[2025.05.28-08.29.04:174][378]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.29.04:186][378]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.28-08.29.04:187][378]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.28-08.29.04:198][378]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.28-08.29.04:199][378]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.28-08.29.04:206][378]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.29.04:206][378]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.04:206][378]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.04:206][378]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.28-08.29.04:213][378]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.29.04:213][378]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.04:213][378]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.04:229][378]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.29.04:259][378]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.29.04:276][378]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.29.04:292][378]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.29.04:295][378]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.29.04:295][378]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.29.04:319][379]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.29.04:461][383]LogStreaming: Display: FlushAsyncLoading(568): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.29.04:461][383]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.28-08.29.04:462][383]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.28-08.29.04:700][383]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.28-08.29.04:706][383]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.28-08.29.04:750][383]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.29.04:752][383]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.29.04:813][383]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.29.04:930][383]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.28-08.29.05:039][383]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.29.05:162][383]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.28-08.29.05:164][383]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.29.05:181][383]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.29.05:267][383]LogSkeletalMesh: Built Skeletal Mesh [0.10s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.29.05:270][383]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.28-08.29.05:278][383]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.29.05:278][383]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.05:278][383]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.05:278][383]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.28-08.29.05:285][383]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.29.05:285][383]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.05:285][383]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.05:301][383]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-08.29.05:333][383]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.29.05:350][383]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.29.05:366][383]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.29.05:413][383]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.29.05:429][383]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.29.05:592][383]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.29.05:594][383]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.29.05:594][383]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.29.05:618][384]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.29.13:823][531]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.29.19:805][639]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.05.28-08.29.23:847][711]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.29.32:239][858]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.29.32:369][858]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.29.33:041][906]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.28-08.29.33:072][907]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.28-08.29.33:238][910]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.28-08.29.33:339][911]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.28-08.29.33:600][915]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.28-08.29.33:702][917]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.28-08.29.33:829][924]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.29.34:778][957]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.28-08.29.34:798][958]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.28-08.29.34:824][959]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.28-08.29.34:849][960]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.28-08.29.34:904][963]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.28-08.29.34:926][964]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.28-08.29.34:951][965]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.28-08.29.34:974][966]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.28-08.29.35:000][967]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.28-08.29.35:022][968]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.28-08.29.35:047][969]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.28-08.29.35:073][970]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.28-08.29.35:098][971]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.28-08.29.35:122][972]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.28-08.29.43:257][505]LogUObjectHash: Compacting FUObjectHashTables data took   0.52ms
[2025.05.28-08.29.44:442][505]LogSlate: Window 'Save Content' being destroyed
[2025.05.28-08.29.44:523][505]LogStall: Shutdown...
[2025.05.28-08.29.44:523][505]LogStall: Shutdown complete.
[2025.05.28-08.29.44:583][505]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.05.28-08.29.44:643][505]Cmd: QUIT_EDITOR
[2025.05.28-08.29.44:643][506]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.28-08.29.44:650][506]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.28-08.29.44:650][506]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.28-08.29.44:650][506]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.28-08.29.44:659][506]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:659][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:659][506]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.05.28-08.29.44:673][506]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.28-08.29.44:673][506]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.28-08.29.44:675][506]LogWorld: UWorld::CleanupWorld for World_34, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:675][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:675][506]LogWorld: UWorld::CleanupWorld for World_33, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:675][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_32, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_31, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_30, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:676][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:676][506]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:677][506]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:677][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:678][506]LogWorld: UWorld::CleanupWorld for World_35, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:678][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:678][506]LogWorld: UWorld::CleanupWorld for World_36, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.29.44:678][506]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.29.44:678][506]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.28-08.29.44:685][506]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.28-08.29.44:685][506]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.28-08.29.44:685][506]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.28-08.29.44:687][506]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.28-08.29.44:687][506]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.28-08.29.44:687][506]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.05.28-08.29.44:687][506]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.28-08.29.44:687][506]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.29.44:689][506]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.29.44:695][506]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.28-08.29.44:695][506]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.28-08.29.44:697][506]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.28-08.29.44:697][506]LogExit: Preparing to exit.
[2025.05.28-08.29.44:769][506]LogUObjectHash: Compacting FUObjectHashTables data took   0.71ms
[2025.05.28-08.29.45:908][506]LogEditorDataStorage: Deinitializing
[2025.05.28-08.29.46:483][506]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.28-08.29.46:495][506]LogExit: Editor shut down
[2025.05.28-08.29.46:499][506]LogExit: Transaction tracking system shut down
[2025.05.28-08.29.46:645][506]LogExit: Object subsystem successfully closed.
[2025.05.28-08.29.46:760][506]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.28-08.29.47:666][506]LogMemoryProfiler: Shutdown
[2025.05.28-08.29.47:666][506]LogNetworkingProfiler: Shutdown
[2025.05.28-08.29.47:666][506]LoadingProfiler: Shutdown
[2025.05.28-08.29.47:666][506]LogTimingProfiler: Shutdown
[2025.05.28-08.29.47:673][506]LogWebBrowser: Deleting browser for Url=file:///D:/UE_5.5/Engine/Plugins/Bridge/ThirdParty/megascans/index.html?token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ouG8aK1hZpvP4SNgAf_L6lmNzqpOPNUB9DMZ8AyN_SkXWKxX3dumAdTrHebYPjBd2cn3GUREeqJkKrDG0yMxJRRH3gblCRN-PO7PmtI1FuDRxnloHtmp-YXXSg-GDWpnZNPsrnbvHT-MSe1pwqU7UXWgldOHaKM9QoXfv6w8MUy_hlKdxKgWbrMc7E7q-mVzZMzpBu-S5g5ma6S_ywZW_m-6lYIJQQWGEFGD4nSwUGPQrdhcG4RKlBKXaMR4yGynjOwyS3An4QH7ZAAM81CJvkai5uOr10XUV3virRiNcLp8MRK_zBVVN5MvMvQhBVpdfIBjnBcEnUQiTQfvhcD5qA&refreshToken=NDA5YjczZDYtOTZhMi00MTg5LTgwN2ItYzEyNzIyYzgyYmJj#/.
[2025.05.28-08.29.47:673][506]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.29.47:673][506]LogBlenderLink: Closing listener socket
[2025.05.28-08.29.47:673][506]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.29.48:039][506]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.28-08.29.48:075][506]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.28-08.29.48:075][506]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7BC7138DDC-4CE6-4470-740E-85AC723CFBE9%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.28-08.29.49:120][506]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.28-08.29.49:125][506]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.28-08.29.49:126][506]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.28-08.29.49:126][506]RenderDocPlugin: plugin has been unloaded.
[2025.05.28-08.29.49:126][506]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.05.28-08.29.49:127][506]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.28-08.29.49:127][506]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.28-08.29.49:127][506]LogPakFile: Destroying PakPlatformFile
[2025.05.28-08.29.49:369][506]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.28-08.29.49:424][506]LogExit: Exiting.
[2025.05.28-08.29.49:439][506]Log file closed, 05/28/25 13:59:49
