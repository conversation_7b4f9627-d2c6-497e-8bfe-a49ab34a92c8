﻿Log file open, 05/28/25 14:14:23
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=37724)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 0.250214
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-FAF7CDDF44D00B7A045E1C975E5FBFA0
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogConfig: Display: Loading VulkanPC ini files took 0.04 seconds
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogConfig: Display: Loading Mac ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.04 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading Windows ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading Unix ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum ********** concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.69ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.05.28-08.44.23:556][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.05.28-08.44.23:556][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.05.28-08.44.23:557][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.05.28-08.44.23:560][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.05.28-08.44.23:560][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.05.28-08.44.23:563][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-08.44.23:563][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-08.44.23:563][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-08.44.23:566][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.05.28-08.44.23:566][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-08.44.23:678][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.05.28-08.44.23:678][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-08.44.23:678][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.05.28-08.44.23:678][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-08.44.23:678][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.05.28-08.44.23:815][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.05.28-08.44.23:815][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.05.28-08.44.23:815][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-08.44.23:815][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.05.28-08.44.23:815][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.05.28-08.44.23:825][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.05.28-08.44.23:825][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.05.28-08.44.23:825][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.05.28-08.44.23:825][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.05.28-08.44.23:825][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.05.28-08.44.23:825][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-08.44.23:825][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.05.28-08.44.23:825][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.05.28-08.44.23:826][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.05.28-08.44.23:826][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.44.23:826][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.05.28-08.44.23:826][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.05.28-08.44.23:826][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-08.44.23:826][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.05.28-08.44.23:826][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.05.28-08.44.23:826][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.05.28-08.44.23:826][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.05.28-08.44.23:826][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.05.28-08.44.23:826][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.05.28-08.44.23:826][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.05.28-08.44.23:826][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.05.28-08.44.23:826][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.05.28-08.44.23:826][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.05.28-08.44.23:826][  0]LogInit: User: Shashank
[2025.05.28-08.44.23:826][  0]LogInit: CPU Page size=4096, Cores=16
[2025.05.28-08.44.23:826][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.05.28-08.44.24:096][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.05.28-08.44.24:096][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.05.28-08.44.24:096][  0]LogMemory: Process Physical Memory: 626.63 MB used, 643.68 MB peak
[2025.05.28-08.44.24:096][  0]LogMemory: Process Virtual Memory: 753.75 MB used, 753.75 MB peak
[2025.05.28-08.44.24:096][  0]LogMemory: Physical Memory: 22626.02 MB used,  42827.38 MB free, 65453.40 MB total
[2025.05.28-08.44.24:096][  0]LogMemory: Virtual Memory: 38025.68 MB used,  31523.72 MB free, 69549.40 MB total
[2025.05.28-08.44.24:096][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.05.28-08.44.24:100][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.05.28-08.44.24:106][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.05.28-08.44.24:106][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.05.28-08.44.24:107][  0]LogInit: Using OS detected language (en-GB).
[2025.05.28-08.44.24:107][  0]LogInit: Using OS detected locale (en-IN).
[2025.05.28-08.44.24:108][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.05.28-08.44.24:108][  0]LogInit: Setting process to per monitor DPI aware
[2025.05.28-08.44.24:374][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.05.28-08.44.24:374][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.05.28-08.44.24:374][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.05.28-08.44.24:387][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.05.28-08.44.24:387][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.05.28-08.44.24:470][  0]LogRHI: Using Default RHI: D3D12
[2025.05.28-08.44.24:470][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.05.28-08.44.24:470][  0]LogRHI: Loading RHI module D3D12RHI
[2025.05.28-08.44.24:470][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.05.28-08.44.24:470][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.05.28-08.44.24:470][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.05.28-08.44.24:470][  0]LogWindows: Attached monitors:
[2025.05.28-08.44.24:470][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-08.44.24:470][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-08.44.24:470][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-08.44.24:470][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-08.44.24:470][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-08.44.24:471][  0]LogRHI: RHI Adapter Info:
[2025.05.28-08.44.24:471][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.05.28-08.44.24:471][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.05.28-08.44.24:471][  0]LogRHI:      Driver Date: 4-25-2025
[2025.05.28-08.44.24:471][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.05.28-08.44.24:495][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.05.28-08.44.24:559][  0]LogNvidiaAftermath: Aftermath initialized
[2025.05.28-08.44.24:559][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.05.28-08.44.24:635][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Bindless resources are supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Raster order views are supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.05.28-08.44.24:635][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.05.28-08.44.24:661][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A92C86F5300)
[2025.05.28-08.44.24:661][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A92C86F5580)
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x00000A92C86F5800)
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.05.28-08.44.24:662][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: Async texture creation enabled
[2025.05.28-08.44.24:662][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.05.28-08.44.24:672][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.05.28-08.44.24:676][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.05.28-08.44.24:682][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.05.28-08.44.24:682][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.05.28-08.44.24:699][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.05.28-08.44.24:699][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.05.28-08.44.24:699][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.05.28-08.44.24:699][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.05.28-08.44.24:722][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.05.28-08.44.24:736][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.05.28-08.44.24:736][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.05.28-08.44.24:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.05.28-08.44.24:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.05.28-08.44.24:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.05.28-08.44.24:750][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.05.28-08.44.24:763][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.05.28-08.44.24:763][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.05.28-08.44.24:763][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.05.28-08.44.24:775][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.05.28-08.44.24:775][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.05.28-08.44.24:775][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.05.28-08.44.24:775][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.05.28-08.44.24:789][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.05.28-08.44.24:789][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.05.28-08.44.24:805][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.05.28-08.44.24:805][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.05.28-08.44.24:805][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.05.28-08.44.24:805][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.05.28-08.44.24:805][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.05.28-08.44.24:843][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.05.28-08.44.24:845][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   VVM_1_0
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.05.28-08.44.24:846][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.05.28-08.44.24:846][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.05.28-08.44.24:846][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.05.28-08.44.24:848][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.05.28-08.44.24:848][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.05.28-08.44.24:848][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.05.28-08.44.24:848][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-08.44.24:848][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.05.28-08.44.24:908][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.05.28-08.44.24:908][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.05.28-08.44.24:908][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.05.28-08.44.24:909][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.05.28-08.44.24:909][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-08.44.24:910][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.05.28-08.44.24:910][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.05.28-08.44.24:910][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 29164 --child-id Zen_29164_Startup'
[2025.05.28-08.44.24:975][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.05.28-08.44.24:975][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.065 seconds
[2025.05.28-08.44.24:975][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.05.28-08.44.24:979][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.00 seconds.
[2025.05.28-08.44.24:979][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.02ms. RandomReadSpeed=2231.07MBs, RandomWriteSpeed=326.29MBs. Assigned SpeedClass 'Local'
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.05.28-08.44.24:981][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.05.28-08.44.24:981][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/29164/).
[2025.05.28-08.44.24:981][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/DF5A091A4A75FEE3DC9AB58203F35AE5/'.
[2025.05.28-08.44.24:981][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.05.28-08.44.24:981][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.05.28-08.44.24:982][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.05.28-08.44.24:982][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.05.28-08.44.25:399][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.05.28-08.44.25:939][  0]LogSlate: Using FreeType 2.10.0
[2025.05.28-08.44.25:939][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.05.28-08.44.25:940][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.44.25:940][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.44.25:940][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.44.25:940][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.44.25:940][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.44.25:940][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.44.25:961][  0]LogAssetRegistry: FAssetRegistry took 0.0020 seconds to start up
[2025.05.28-08.44.25:963][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.05.28-08.44.25:968][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.001s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.05.28-08.44.25:968][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.05.28-08.44.26:142][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.44.26:144][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.05.28-08.44.26:144][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.05.28-08.44.26:144][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.05.28-08.44.26:154][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.05.28-08.44.26:154][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.05.28-08.44.26:177][  0]LogDeviceProfileManager: Active device profile: [00000A92E86CCE00][00000A92E67F0000 66] WindowsEditor
[2025.05.28-08.44.26:177][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.05.28-08.44.26:178][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.05.28-08.44.26:180][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.05.28-08.44.26:180][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.05.28-08.44.26:209][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:209][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.05.28-08.44.26:210][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:210][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:210][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.05.28-08.44.26:210][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:210][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.44.26:211][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:212][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.05.28-08.44.26:213][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.05.28-08.44.26:214][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.05.28-08.44.26:214][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:214][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.05.28-08.44.26:214][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.05.28-08.44.26:214][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.05.28-08.44.26:359][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.05.28-08.44.26:359][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.05.28-08.44.26:359][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.05.28-08.44.26:359][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.05.28-08.44.26:359][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.05.28-08.44.26:479][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.52ms
[2025.05.28-08.44.26:497][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.56ms
[2025.05.28-08.44.26:507][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.49ms
[2025.05.28-08.44.26:509][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.49ms
[2025.05.28-08.44.26:700][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.05.28-08.44.26:700][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.05.28-08.44.26:704][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.05.28-08.44.26:704][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.05.28-08.44.26:705][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.05.28-08.44.26:708][  0]LogLiveCoding: Display: Waiting for server
[2025.05.28-08.44.26:720][  0]LogSlate: Border
[2025.05.28-08.44.26:720][  0]LogSlate: BreadcrumbButton
[2025.05.28-08.44.26:720][  0]LogSlate: Brushes.Title
[2025.05.28-08.44.26:720][  0]LogSlate: Default
[2025.05.28-08.44.26:720][  0]LogSlate: Icons.Save
[2025.05.28-08.44.26:720][  0]LogSlate: Icons.Toolbar.Settings
[2025.05.28-08.44.26:720][  0]LogSlate: ListView
[2025.05.28-08.44.26:720][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.05.28-08.44.26:720][  0]LogSlate: SoftwareCursor_Grab
[2025.05.28-08.44.26:720][  0]LogSlate: TableView.DarkRow
[2025.05.28-08.44.26:720][  0]LogSlate: TableView.Row
[2025.05.28-08.44.26:720][  0]LogSlate: TreeView
[2025.05.28-08.44.26:773][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.05.28-08.44.26:782][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.05.28-08.44.26:783][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 1.811 ms
[2025.05.28-08.44.26:791][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.48ms
[2025.05.28-08.44.26:804][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.05.28-08.44.26:804][  0]LogInit: XR: MultiViewport is Disabled
[2025.05.28-08.44.26:804][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.05.28-08.44.26:804][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.05.28-08.44.26:879][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.50ms
[2025.05.28-08.44.27:111][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-08.44.27:111][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.05.28-08.44.27:139][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: 3EB44808F9A74E2D8000000000005E00 | Instance: 8642E9684F3B7B9A64CCC28BD18A9AA5 (DESKTOP-E41IK6R-29164).
[2025.05.28-08.44.27:309][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.05.28-08.44.27:314][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.05.28-08.44.27:314][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.05.28-08.44.27:314][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:53808'.
[2025.05.28-08.44.27:316][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.05.28-08.44.27:316][  0]LogUdpMessaging: Display: Added local interface '172.27.16.1' to multicast group '230.0.0.1:6666'
[2025.05.28-08.44.27:444][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.05.28-08.44.27:445][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.05.28-08.44.27:445][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.05.28-08.44.27:445][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.05.28-08.44.27:445][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.05.28-08.44.27:493][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.05.28-08.44.27:543][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.05.28-08.44.27:543][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.05.28-08.44.27:560][  0]LogMetaSound: MetaSound Engine Initialized
[2025.05.28-08.44.27:689][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.05.28-08.44.27:911][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.05.28-08.44.28:342][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.05.28-08.44.28:417][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.05.28-08.44.34:756][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.34:770][  0]LogSkeletalMesh: Built Skeletal Mesh [6.43s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.05.28-08.44.34:814][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.44.34:814][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.44.34:815][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.44.34:815][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.44.34:815][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.44.34:815][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.44.34:941][  0]SourceControl: Revision control is disabled
[2025.05.28-08.44.34:952][  0]SourceControl: Revision control is disabled
[2025.05.28-08.44.34:973][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.50ms
[2025.05.28-08.44.34:979][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.47ms
[2025.05.28-08.44.35:090][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-08.44.35:090][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-08.44.35:111][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.05.28-08.44.35:120][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.05.28-08.44.35:132][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-08.44.35:132][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.05.28-08.44.35:177][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.05.28-08.44.35:177][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.05.28-08.44.35:177][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.05.28-08.44.35:178][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.05.28-08.44.35:178][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.05.28-08.44.35:178][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.05.28-08.44.35:178][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.05.28-08.44.35:179][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.05.28-08.44.35:179][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.05.28-08.44.35:179][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.05.28-08.44.35:179][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.05.28-08.44.35:179][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.05.28-08.44.35:180][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.05.28-08.44.35:180][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.05.28-08.44.35:180][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.05.28-08.44.35:181][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.05.28-08.44.35:181][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.05.28-08.44.35:181][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.05.28-08.44.35:181][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.05.28-08.44.35:182][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.05.28-08.44.35:182][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.05.28-08.44.35:183][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.05.28-08.44.35:184][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.05.28-08.44.35:184][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.05.28-08.44.35:185][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.05.28-08.44.35:185][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.05.28-08.44.35:185][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.05.28-08.44.35:185][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.05.28-08.44.35:185][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.05.28-08.44.35:212][  0]LogCollectionManager: Loaded 0 collections in 0.000720 seconds
[2025.05.28-08.44.35:214][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.05.28-08.44.35:216][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.05.28-08.44.35:218][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.05.28-08.44.35:263][  0]LogBlenderLink: Waiting for client connection...
[2025.05.28-08.44.35:283][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.05.28-08.44.35:283][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.05.28-08.44.35:283][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.05.28-08.44.35:283][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.05.28-08.44.35:283][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.05.28-08.44.35:283][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.05.28-08.44.35:295][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.05.28-08.44.35:295][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.05.28-08.44.35:308][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-05-28T08:44:35.308Z using C
[2025.05.28-08.44.35:308][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.05.28-08.44.35:308][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-08.44.35:309][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.05.28-08.44.35:314][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.05.28-08.44.35:315][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.05.28-08.44.35:315][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.05.28-08.44.35:315][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.000055
[2025.05.28-08.44.35:315][  0]LogFab: Display: Logging in using persist
[2025.05.28-08.44.35:315][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.05.28-08.44.35:344][  0]LogUObjectArray: 52282 objects as part of root set at end of initial load.
[2025.05.28-08.44.35:344][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.05.28-08.44.35:356][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.05.28-08.44.35:356][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.05.28-08.44.35:466][  0]LogEngine: Initializing Engine...
[2025.05.28-08.44.35:470][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.05.28-08.44.35:470][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.000 s
[2025.05.28-08.44.35:537][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.05.28-08.44.35:549][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.05.28-08.44.35:561][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.05.28-08.44.35:561][  0]LogInit: Texture streaming: Enabled
[2025.05.28-08.44.35:568][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.05.28-08.44.35:579][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.05.28-08.44.35:584][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.05.28-08.44.35:584][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.05.28-08.44.35:584][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.05.28-08.44.35:584][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.05.28-08.44.35:584][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.05.28-08.44.35:584][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.05.28-08.44.35:585][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.05.28-08.44.35:585][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.05.28-08.44.35:585][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.05.28-08.44.35:589][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.05.28-08.44.35:646][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.05.28-08.44.35:647][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.05.28-08.44.35:648][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.05.28-08.44.35:648][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.05.28-08.44.35:649][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.05.28-08.44.35:649][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.05.28-08.44.35:652][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.05.28-08.44.35:652][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.05.28-08.44.35:652][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.05.28-08.44.35:652][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.05.28-08.44.35:652][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.05.28-08.44.35:657][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.05.28-08.44.35:660][  0]LogInit: Undo buffer set to 256 MB
[2025.05.28-08.44.35:660][  0]LogInit: Transaction tracking system initialized
[2025.05.28-08.44.35:670][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.05.28-08.44.35:712][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.52ms
[2025.05.28-08.44.35:714][  0]LocalizationService: Localization service is disabled
[2025.05.28-08.44.35:724][  0]LogTimingProfiler: Initialize
[2025.05.28-08.44.35:724][  0]LogTimingProfiler: OnSessionChanged
[2025.05.28-08.44.35:724][  0]LoadingProfiler: Initialize
[2025.05.28-08.44.35:724][  0]LoadingProfiler: OnSessionChanged
[2025.05.28-08.44.35:724][  0]LogNetworkingProfiler: Initialize
[2025.05.28-08.44.35:724][  0]LogNetworkingProfiler: OnSessionChanged
[2025.05.28-08.44.35:724][  0]LogMemoryProfiler: Initialize
[2025.05.28-08.44.35:724][  0]LogMemoryProfiler: OnSessionChanged
[2025.05.28-08.44.35:835][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.05.28-08.44.35:846][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.05.28-08.44.35:872][  0]LogPython: Using Python 3.11.8
[2025.05.28-08.44.36:836][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.05.28-08.44.36:859][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.05.28-08.44.36:867][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.05.28-08.44.36:912][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.05.28-08.44.36:912][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.05.28-08.44.36:962][  0]LogEditorDataStorage: Initializing
[2025.05.28-08.44.36:962][  0]LogEditorDataStorage: Initialized
[2025.05.28-08.44.36:963][  0]LogWindows: Attached monitors:
[2025.05.28-08.44.36:963][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.05.28-08.44.36:963][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.05.28-08.44.36:963][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.05.28-08.44.36:963][  0]LogWindows: Found 3 attached monitors.
[2025.05.28-08.44.36:963][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.05.28-08.44.36:973][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.05.28-08.44.36:975][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.05.28-08.44.36:975][  0]SourceControl: Revision control is disabled
[2025.05.28-08.44.36:975][  0]LogUnrealEdMisc: Loading editor; pre map load, took 14.174
[2025.05.28-08.44.36:976][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.05.28-08.44.36:978][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.44.36:978][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.44.37:021][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.05.28-08.44.37:022][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.61ms
[2025.05.28-08.44.37:029][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.05.28-08.44.37:029][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.05.28-08.44.37:030][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.05.28-08.44.37:030][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.05.28-08.44.37:030][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.05.28-08.44.37:761][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.05.28-08.44.39:967][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.44.39:971][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.44.39:973][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.44.39:974][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.05.28-08.44.39:974][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.05.28-08.44.39:974][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.05.28-08.44.39:976][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.05.28-08.44.41:987][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.05.28-08.44.42:030][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.05.28-08.44.42:409][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.42:412][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.05.28-08.44.42:425][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.05.28-08.44.42:426][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.05.28-08.44.42:819][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.42:820][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.05.28-08.44.43:882][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.43:886][  0]LogSkeletalMesh: Built Skeletal Mesh [1.46s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.05.28-08.44.44:001][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.05.28-08.44.44:210][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.05.28-08.44.44:214][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.44:219][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.05.28-08.44.44:534][  0]LogWorldPartition: Display: WorldPartition initialize took 7.50 sec
[2025.05.28-08.44.44:612][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.05.28-08.44.49:639][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.05.28-08.44.49:654][  0]LogSkeletalMesh: Built Skeletal Mesh [5.45s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.05.28-08.44.50:321][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.05.28-08.44.50:570][  0]LogUObjectHash: Compacting FUObjectHashTables data took   1.43ms
[2025.05.28-08.44.50:572][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.05.28-08.44.50:573][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 1.613ms to complete.
[2025.05.28-08.44.50:589][  0]LogUnrealEdMisc: Total Editor Startup Time, took 27.787
[2025.05.28-08.44.50:775][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.05.28-08.44.50:874][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.44.50:941][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.44.50:991][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.44.51:046][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.05.28-08.44.51:078][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:078][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.05.28-08.44.51:079][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:079][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.05.28-08.44.51:079][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:079][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:079][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:079][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:079][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:079][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.05.28-08.44.51:079][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:080][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:080][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:080][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.05.28-08.44.51:080][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:080][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:080][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:080][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:080][  0]LogPakFile: Initializing PakPlatformFile
[2025.05.28-08.44.51:080][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.05.28-08.44.51:125][  0]LogSlate: Took 0.000103 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.05.28-08.44.51:337][  0]LogWebBrowser: Loaded CEF3 version 90.6.7.2358 from D:/UE_5.5/Engine/Binaries/ThirdParty/CEF3/Win64
[2025.05.28-08.44.51:337][  0]LogCEFBrowser: CEF GPU acceleration enabled
[2025.05.28-08.44.51:429][  0]LogSlate: Took 0.000101 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.05.28-08.44.51:431][  0]LogSlate: Took 0.000095 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.05.28-08.44.51:433][  0]LogSlate: Took 0.000083 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.05.28-08.44.51:464][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.05.28-08.44.51:466][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.05.28-08.44.51:467][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.05.28-08.44.51:467][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-08.44.51:533][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-08.44.51:533][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.05.28-08.44.51:533][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.05.28-08.44.51:533][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.05.28-08.44.51:533][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.05.28-08.44.51:620][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.05.28-08.44.51:620][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.05.28-08.44.51:655][  0]LogSlate: Took 0.000939 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.05.28-08.44.51:981][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 30.38 ms. Compile time 15.27 ms, link time 14.62 ms.
[2025.05.28-08.44.52:277][  0]LogStall: Startup...
[2025.05.28-08.44.52:280][  0]LogStall: Startup complete.
[2025.05.28-08.44.52:285][  0]LogLoad: (Engine Initialization) Total time: 29.48 seconds
[2025.05.28-08.44.52:499][  0]LogAssetRegistry: AssetRegistryGather time 0.0824s: AssetDataDiscovery 0.0139s, AssetDataGather 0.0113s, StoreResults 0.0572s. Wall time 26.5390s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8092. NumUncachedFiles 0.
	BackgroundTickInterruptions 0.
[2025.05.28-08.44.52:518][  0]LogSourceControl: Uncontrolled asset enumeration started...
[2025.05.28-08.44.52:518][  0]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.05.28-08.44.52:640][  0]LogSourceControl: Uncontrolled asset enumeration finished in 0.122179 seconds (Found 8068 uncontrolled assets)
[2025.05.28-08.44.52:701][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.05.28-08.44.52:701][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.05.28-08.44.52:895][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.44.52:899][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.05.28-08.44.52:899][  0]LogFab: Display: Logging in using exchange code
[2025.05.28-08.44.52:899][  0]LogFab: Display: Reading exchange code from commandline
[2025.05.28-08.44.52:899][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.05.28-08.44.52:899][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.05.28-08.44.52:928][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.05.28-08.44.52:930][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 31.650 ms
[2025.05.28-08.44.52:945][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.05.28-08.44.53:242][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.05.28-08.44.53:987][ 13]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 18.617115
[2025.05.28-08.44.53:988][ 13]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.05.28-08.44.53:989][ 13]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 18.672287
[2025.05.28-08.44.54:447][ 21]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.05.28-08.44.55:004][ 31]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 19.638744
[2025.05.28-08.44.55:006][ 31]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 607272702
[2025.05.28-08.44.55:006][ 31]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 19.638744, Update Interval: 359.595337
[2025.05.28-08.45.02:306][330]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.45.12:309][301]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.45.22:350][ 97]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.45.31:524][248]Cmd: Interchange.FeatureFlags.Import.FBX Flase
[2025.05.28-08.45.31:524][248]Interchange.FeatureFlags.Import.FBX = "false"
[2025.05.28-08.45.32:316][311]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.45.37:858][836]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.37:858][836]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset (0x8306E6F3EEA7F81A) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.37:858][836]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/cartilage_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.37:928][836]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx)
[2025.05.28-08.45.37:935][836]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/cartilage_lod0_mesh.fbx
[2025.05.28-08.45.37:951][836]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.37:952][836]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_cartilage_shader.MH_Friend_cartilage_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.37:964][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2BDC00 with pending SubmitJob call.
[2025.05.28-08.45.37:965][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45A000 with pending SubmitJob call.
[2025.05.28-08.45.37:966][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EB459600 with pending SubmitJob call.
[2025.05.28-08.45.37:966][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45F000 with pending SubmitJob call.
[2025.05.28-08.45.37:966][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45AA00 with pending SubmitJob call.
[2025.05.28-08.45.37:968][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2BF000 with pending SubmitJob call.
[2025.05.28-08.45.37:968][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF316E00 with pending SubmitJob call.
[2025.05.28-08.45.37:968][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F1718C00 with pending SubmitJob call.
[2025.05.28-08.45.37:969][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F171C800 with pending SubmitJob call.
[2025.05.28-08.45.37:969][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2B2800 with pending SubmitJob call.
[2025.05.28-08.45.37:970][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45B400 with pending SubmitJob call.
[2025.05.28-08.45.37:970][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF310A00 with pending SubmitJob call.
[2025.05.28-08.45.37:970][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F171D200 with pending SubmitJob call.
[2025.05.28-08.45.37:971][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F1719600 with pending SubmitJob call.
[2025.05.28-08.45.37:972][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF78D200 with pending SubmitJob call.
[2025.05.28-08.45.37:973][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F1714600 with pending SubmitJob call.
[2025.05.28-08.45.37:973][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2BC800 with pending SubmitJob call.
[2025.05.28-08.45.37:973][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF318200 with pending SubmitJob call.
[2025.05.28-08.45.37:973][836]LogShaderCompilers: Display: Cancelled job 0x00000A93EF313200 with pending SubmitJob call.
[2025.05.28-08.45.37:974][836]LogFbx: Triangulating skeletal mesh cartilage_lod0_mesh
[2025.05.28-08.45.37:974][836]LogShaderCompilers: Display: Cancelled job 0x00000A93F1711400 with pending SubmitJob call.
[2025.05.28-08.45.37:988][836]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.37:995][836]LogSkeletalMesh: Section 0: Material=0, 576 triangles
[2025.05.28-08.45.37:997][836]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.45.38:003][836]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.45.38:004][836]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.05.28-08.45.38:012][836]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_0:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.38:012][836]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.38:012][836]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.38:012][836]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.05.28-08.45.38:021][836]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_1:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.38:021][836]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.38:021][836]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.38:113][836]LogUObjectHash: Compacting FUObjectHashTables data took   0.79ms
[2025.05.28-08.45.38:148][836]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.45.38:167][836]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.45.38:182][836]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-08.45.38:212][836]LogSkeletalMesh: Building Skeletal Mesh cartilage_lod0_mesh...
[2025.05.28-08.45.38:227][836]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/cartilage_lod0_mesh.cartilage_lod0_mesh
[2025.05.28-08.45.38:229][836]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.38:229][836]FBXImport: Warning: The bone size is too small to create Physics Asset 'cartilage_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'cartilage_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.38:310][837]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.28-08.45.38:439][841]LogStreaming: Display: FlushAsyncLoading(522): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.38:439][841]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset (0x66E38450F451F47D) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.38:439][841]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.38:659][841]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx)
[2025.05.28-08.45.38:663][841]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeEdge_lod0_mesh.fbx
[2025.05.28-08.45.38:666][841]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.38:667][841]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeEdge_shader.MH_Friend_eyeEdge_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.38:679][841]LogShaderCompilers: Display: Cancelled job 0x00000A93CD0FF000 with pending SubmitJob call.
[2025.05.28-08.45.38:679][841]LogShaderCompilers: Display: Cancelled job 0x00000A93A291E600 with pending SubmitJob call.
[2025.05.28-08.45.38:679][841]LogShaderCompilers: Display: Cancelled job 0x00000A93CD0FE600 with pending SubmitJob call.
[2025.05.28-08.45.38:682][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1571E00 with pending SubmitJob call.
[2025.05.28-08.45.38:682][841]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2BF000 with pending SubmitJob call.
[2025.05.28-08.45.38:683][841]LogShaderCompilers: Display: Cancelled job 0x00000A93DD7C1400 with pending SubmitJob call.
[2025.05.28-08.45.38:683][841]LogShaderCompilers: Display: Cancelled job 0x00000A93EF313200 with pending SubmitJob call.
[2025.05.28-08.45.38:683][841]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7BD200 with pending SubmitJob call.
[2025.05.28-08.45.38:683][841]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2B1400 with pending SubmitJob call.
[2025.05.28-08.45.38:683][841]LogShaderCompilers: Display: Cancelled job 0x00000A93CD0FC800 with pending SubmitJob call.
[2025.05.28-08.45.38:684][841]LogShaderCompilers: Display: Cancelled job 0x00000A93A291D200 with pending SubmitJob call.
[2025.05.28-08.45.38:684][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1578200 with pending SubmitJob call.
[2025.05.28-08.45.38:684][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F157A000 with pending SubmitJob call.
[2025.05.28-08.45.38:685][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1570A00 with pending SubmitJob call.
[2025.05.28-08.45.38:686][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1571400 with pending SubmitJob call.
[2025.05.28-08.45.38:686][841]LogShaderCompilers: Display: Cancelled job 0x00000A93CD0FDC00 with pending SubmitJob call.
[2025.05.28-08.45.38:686][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1575000 with pending SubmitJob call.
[2025.05.28-08.45.38:686][841]LogFbx: Triangulating skeletal mesh eyeEdge_lod0_mesh
[2025.05.28-08.45.38:687][841]LogShaderCompilers: Display: Cancelled job 0x00000A93F1576E00 with pending SubmitJob call.
[2025.05.28-08.45.38:688][841]LogShaderCompilers: Display: Cancelled job 0x00000A93CD0F6E00 with pending SubmitJob call.
[2025.05.28-08.45.38:688][841]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2B5000 with pending SubmitJob call.
[2025.05.28-08.45.38:696][841]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.38:701][841]LogSkeletalMesh: Section 0: Material=0, 386 triangles
[2025.05.28-08.45.38:702][841]LogSkeletalMesh: Building Skeletal Mesh eyeEdge_lod0_mesh...
[2025.05.28-08.45.38:706][841]LogSkeletalMesh: Built Skeletal Mesh [0.00s] /Game/untitled_category/untitled_asset/eyeEdge_lod0_mesh.eyeEdge_lod0_mesh
[2025.05.28-08.45.38:707][841]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.05.28-08.45.38:714][841]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.38:714][841]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.38:714][841]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.38:715][841]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.05.28-08.45.38:721][841]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_3:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.38:721][841]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.38:721][841]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.38:738][841]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.45.38:770][841]LogUObjectHash: Compacting FUObjectHashTables data took   0.73ms
[2025.05.28-08.45.38:787][841]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.45.38:803][841]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.45.38:806][841]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.38:806][841]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeEdge_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeEdge_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.38:831][842]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.05.28-08.45.38:955][846]LogStreaming: Display: FlushAsyncLoading(528): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.38:955][846]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset (0x3503C37BEAAD4328) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.38:955][846]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.39:166][846]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx)
[2025.05.28-08.45.39:171][846]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeLeft_lod0_mesh.fbx
[2025.05.28-08.45.39:175][846]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.39:176][846]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeLeft_shader.MH_Friend_eyeLeft_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.39:237][846]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.45.39:352][846]LogFbx: Triangulating skeletal mesh eyeLeft_lod0_mesh
[2025.05.28-08.45.39:375][846]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.39:400][846]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.45.39:402][846]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.45.39:418][846]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.45.39:423][846]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.45.39:425][846]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.05.28-08.45.39:433][846]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_4:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.39:433][846]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.39:433][846]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.39:434][846]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.05.28-08.45.39:441][846]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_5:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.39:441][846]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.39:441][846]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.39:458][846]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.45.39:491][846]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-08.45.39:507][846]LogUObjectHash: Compacting FUObjectHashTables data took   0.67ms
[2025.05.28-08.45.39:523][846]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-08.45.39:531][846]LogSkeletalMesh: Building Skeletal Mesh eyeLeft_lod0_mesh...
[2025.05.28-08.45.39:548][846]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeLeft_lod0_mesh) ...
[2025.05.28-08.45.39:564][846]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeLeft_lod0_mesh.eyeLeft_lod0_mesh
[2025.05.28-08.45.39:565][846]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.39:565][846]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeLeft_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeLeft_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.39:594][847]LogUObjectHash: Compacting FUObjectHashTables data took   0.94ms
[2025.05.28-08.45.39:666][850]LogStreaming: Display: FlushAsyncLoading(536): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.39:666][850]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset (0xB68C5B7FAED625F8) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.39:666][850]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeRight_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.39:876][850]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx)
[2025.05.28-08.45.39:882][850]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeRight_lod0_mesh.fbx
[2025.05.28-08.45.39:886][850]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.39:887][850]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeRight_shader.MH_Friend_eyeRight_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.39:913][850]LogFbxMaterialImport: Warning: Manual texture reimport and recompression may be needed for eyes_normal_map
[2025.05.28-08.45.39:951][850]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.45.40:071][850]LogFbx: Triangulating skeletal mesh eyeRight_lod0_mesh
[2025.05.28-08.45.40:094][850]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.40:120][850]LogSkeletalMesh: Section 0: Material=0, 1536 triangles
[2025.05.28-08.45.40:121][850]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.45.40:138][850]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.45.40:143][850]LogSkeletalMesh: Built Skeletal Mesh [0.02s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.45.40:144][850]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.05.28-08.45.40:151][850]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_6:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.40:151][850]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.40:151][850]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.40:152][850]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.05.28-08.45.40:158][850]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_7:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.40:158][850]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.40:158][850]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.40:176][850]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.05.28-08.45.40:209][850]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.05.28-08.45.40:227][850]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-08.45.40:243][850]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.45.40:251][850]LogSkeletalMesh: Building Skeletal Mesh eyeRight_lod0_mesh...
[2025.05.28-08.45.40:266][850]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (eyeRight_lod0_mesh) ...
[2025.05.28-08.45.40:283][850]LogSkeletalMesh: Built Skeletal Mesh [0.03s] /Game/untitled_category/untitled_asset/eyeRight_lod0_mesh.eyeRight_lod0_mesh
[2025.05.28-08.45.40:283][850]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.40:283][850]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeRight_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeRight_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.40:307][851]LogUObjectHash: Compacting FUObjectHashTables data took   0.85ms
[2025.05.28-08.45.40:436][855]LogStreaming: Display: FlushAsyncLoading(542): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.40:436][855]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset (0xC8AA4E7A36074A88) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.40:436][855]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyelashes_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.40:660][855]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx)
[2025.05.28-08.45.40:666][855]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyelashes_lod0_mesh.fbx
[2025.05.28-08.45.40:669][855]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.40:670][855]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyelashes_shader.MH_Friend_eyelashes_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.40:683][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1718C00 with pending SubmitJob call.
[2025.05.28-08.45.40:684][855]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7BB400 with pending SubmitJob call.
[2025.05.28-08.45.40:684][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1710000 with pending SubmitJob call.
[2025.05.28-08.45.40:684][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1716E00 with pending SubmitJob call.
[2025.05.28-08.45.40:686][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1573200 with pending SubmitJob call.
[2025.05.28-08.45.40:686][855]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7BD200 with pending SubmitJob call.
[2025.05.28-08.45.40:687][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1578200 with pending SubmitJob call.
[2025.05.28-08.45.40:687][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF783200 with pending SubmitJob call.
[2025.05.28-08.45.40:687][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1578C00 with pending SubmitJob call.
[2025.05.28-08.45.40:687][855]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7B8C00 with pending SubmitJob call.
[2025.05.28-08.45.40:688][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF780000 with pending SubmitJob call.
[2025.05.28-08.45.40:688][855]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7B7800 with pending SubmitJob call.
[2025.05.28-08.45.40:688][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF782800 with pending SubmitJob call.
[2025.05.28-08.45.40:688][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1712800 with pending SubmitJob call.
[2025.05.28-08.45.40:688][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF784600 with pending SubmitJob call.
[2025.05.28-08.45.40:690][855]LogShaderCompilers: Display: Cancelled job 0x00000A936039AA00 with pending SubmitJob call.
[2025.05.28-08.45.40:691][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1576400 with pending SubmitJob call.
[2025.05.28-08.45.40:691][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF78C800 with pending SubmitJob call.
[2025.05.28-08.45.40:692][855]LogShaderCompilers: Display: Cancelled job 0x00000A93EF78D200 with pending SubmitJob call.
[2025.05.28-08.45.40:692][855]LogFbx: Triangulating skeletal mesh eyelashes_lod0_mesh
[2025.05.28-08.45.40:692][855]LogShaderCompilers: Display: Cancelled job 0x00000A93F1570A00 with pending SubmitJob call.
[2025.05.28-08.45.40:717][855]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.40:734][855]LogSkeletalMesh: Section 0: Material=0, 1722 triangles
[2025.05.28-08.45.40:735][855]LogSkeletalMesh: Building Skeletal Mesh eyelashes_lod0_mesh...
[2025.05.28-08.45.40:746][855]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyelashes_lod0_mesh.eyelashes_lod0_mesh
[2025.05.28-08.45.40:748][855]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.05.28-08.45.40:755][855]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_8:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.40:755][855]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.40:755][855]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.40:755][855]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.05.28-08.45.40:762][855]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_9:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.40:762][855]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.40:762][855]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.40:779][855]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.45.40:813][855]LogUObjectHash: Compacting FUObjectHashTables data took   0.80ms
[2025.05.28-08.45.40:830][855]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.28-08.45.40:847][855]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-08.45.40:851][855]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.40:851][855]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyelashes_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyelashes_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.40:875][856]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.45.40:940][859]LogStreaming: Display: FlushAsyncLoading(548): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.40:940][859]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset (0xA8E5BC927DC4F667) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.40:940][859]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/eyeshell_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.41:150][859]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx)
[2025.05.28-08.45.41:156][859]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/eyeshell_lod0_mesh.fbx
[2025.05.28-08.45.41:159][859]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.41:160][859]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_eyeshell_shader.MH_Friend_eyeshell_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.41:171][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF783200 with pending SubmitJob call.
[2025.05.28-08.45.41:173][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF782800 with pending SubmitJob call.
[2025.05.28-08.45.41:174][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1578200 with pending SubmitJob call.
[2025.05.28-08.45.41:175][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF78DC00 with pending SubmitJob call.
[2025.05.28-08.45.41:175][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45F000 with pending SubmitJob call.
[2025.05.28-08.45.41:176][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF781400 with pending SubmitJob call.
[2025.05.28-08.45.41:176][859]LogFbx: Triangulating skeletal mesh eyeshell_lod0_mesh
[2025.05.28-08.45.41:177][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45A000 with pending SubmitJob call.
[2025.05.28-08.45.41:178][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1576400 with pending SubmitJob call.
[2025.05.28-08.45.41:178][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1571E00 with pending SubmitJob call.
[2025.05.28-08.45.41:178][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1573200 with pending SubmitJob call.
[2025.05.28-08.45.41:178][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45AA00 with pending SubmitJob call.
[2025.05.28-08.45.41:178][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1576E00 with pending SubmitJob call.
[2025.05.28-08.45.41:179][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1575A00 with pending SubmitJob call.
[2025.05.28-08.45.41:179][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1571400 with pending SubmitJob call.
[2025.05.28-08.45.41:180][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EB45C800 with pending SubmitJob call.
[2025.05.28-08.45.41:180][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF78D200 with pending SubmitJob call.
[2025.05.28-08.45.41:182][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1572800 with pending SubmitJob call.
[2025.05.28-08.45.41:182][859]LogShaderCompilers: Display: Cancelled job 0x00000A93F1573C00 with pending SubmitJob call.
[2025.05.28-08.45.41:182][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EB458200 with pending SubmitJob call.
[2025.05.28-08.45.41:183][859]LogShaderCompilers: Display: Cancelled job 0x00000A93EF783C00 with pending SubmitJob call.
[2025.05.28-08.45.41:195][859]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.41:209][859]LogSkeletalMesh: Section 0: Material=0, 980 triangles
[2025.05.28-08.45.41:210][859]LogSkeletalMesh: Building Skeletal Mesh eyeshell_lod0_mesh...
[2025.05.28-08.45.41:221][859]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/eyeshell_lod0_mesh.eyeshell_lod0_mesh
[2025.05.28-08.45.41:222][859]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.05.28-08.45.41:229][859]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_10:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.41:229][859]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.41:229][859]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.41:230][859]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.05.28-08.45.41:236][859]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_11:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.41:236][859]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.41:236][859]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.41:253][859]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.45.41:284][859]LogUObjectHash: Compacting FUObjectHashTables data took   0.75ms
[2025.05.28-08.45.41:302][859]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.45.41:318][859]LogUObjectHash: Compacting FUObjectHashTables data took   0.38ms
[2025.05.28-08.45.41:321][859]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.41:322][859]FBXImport: Warning: The bone size is too small to create Physics Asset 'eyeshell_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'eyeshell_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.41:346][860]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.45.41:469][864]LogStreaming: Display: FlushAsyncLoading(554): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.41:469][864]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset (0xE7DA988AE0F17ACA) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.41:469][864]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/head_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.41:756][864]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx)
[2025.05.28-08.45.41:826][864]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/head_lod0_mesh.fbx
[2025.05.28-08.45.45:200][864]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.45:203][864]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_head_shader.MH_Friend_head_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.45:263][864]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.45.45:378][864]LogFbx: Triangulating skeletal mesh head_lod0_mesh
[2025.05.28-08.45.46:718][864]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.46:945][864]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.45.47:439][864]LogSkeletalMesh: Section 0: Material=0, 48004 triangles
[2025.05.28-08.45.47:506][864]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.45.47:523][864]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.45.47:575][864]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.45.48:084][864]LogSkeletalMesh: Built Skeletal Mesh [0.58s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.45.48:089][864]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.05.28-08.45.48:098][864]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_12:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.48:099][864]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.48:099][864]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.48:101][864]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.05.28-08.45.48:107][864]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_13:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.48:107][864]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.48:107][864]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.48:125][864]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-08.45.48:157][864]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.45.48:175][864]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.45.48:191][864]LogUObjectHash: Compacting FUObjectHashTables data took   0.36ms
[2025.05.28-08.45.51:291][864]LogSkeletalMesh: Building Skeletal Mesh head_lod0_mesh...
[2025.05.28-08.45.51:307][864]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.45.51:588][864]LogSkeletalMesh: Display: /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh ComputeTangents MikkTSpace function: Compute a zero length normal vector.
[2025.05.28-08.45.55:363][864]LogSkeletalMesh: Built Skeletal Mesh [4.07s] /Game/untitled_category/untitled_asset/head_lod0_mesh.head_lod0_mesh
[2025.05.28-08.45.55:491][864]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.55:491][864]FBXImport: Warning: The bone size is too small to create Physics Asset 'head_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'head_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.55:517][865]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (head_lod0_mesh) ...
[2025.05.28-08.45.55:566][865]LogUObjectHash: Compacting FUObjectHashTables data took   1.07ms
[2025.05.28-08.45.55:578][865]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.45.55:658][868]LogStreaming: Display: FlushAsyncLoading(562): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.55:658][868]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset (0xCEBEF7E7EA79F106) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.55:658][868]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/saliva_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.55:886][868]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx)
[2025.05.28-08.45.55:890][868]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/saliva_lod0_mesh.fbx
[2025.05.28-08.45.55:893][868]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.55:894][868]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_saliva_shader.MH_Friend_saliva_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.55:906][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF8C00 with pending SubmitJob call.
[2025.05.28-08.45.55:906][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF6E00 with pending SubmitJob call.
[2025.05.28-08.45.55:906][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFDC00 with pending SubmitJob call.
[2025.05.28-08.45.55:907][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF6400 with pending SubmitJob call.
[2025.05.28-08.45.55:909][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF3C00 with pending SubmitJob call.
[2025.05.28-08.45.55:909][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF4600 with pending SubmitJob call.
[2025.05.28-08.45.55:910][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFBE00 with pending SubmitJob call.
[2025.05.28-08.45.55:910][868]LogShaderCompilers: Display: Cancelled job 0x00000A93EF2B5000 with pending SubmitJob call.
[2025.05.28-08.45.55:910][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFA000 with pending SubmitJob call.
[2025.05.28-08.45.55:912][868]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7B6E00 with pending SubmitJob call.
[2025.05.28-08.45.55:912][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF0A00 with pending SubmitJob call.
[2025.05.28-08.45.55:912][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFF000 with pending SubmitJob call.
[2025.05.28-08.45.55:912][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF9600 with pending SubmitJob call.
[2025.05.28-08.45.55:913][868]LogShaderCompilers: Display: Cancelled job 0x00000A93DD7C3C00 with pending SubmitJob call.
[2025.05.28-08.45.55:913][868]LogShaderCompilers: Display: Cancelled job 0x00000A92D60E1400 with pending SubmitJob call.
[2025.05.28-08.45.55:913][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DF2800 with pending SubmitJob call.
[2025.05.28-08.45.55:914][868]LogFbx: Triangulating skeletal mesh saliva_lod0_mesh
[2025.05.28-08.45.55:914][868]LogShaderCompilers: Display: Cancelled job 0x00000A93F1572800 with pending SubmitJob call.
[2025.05.28-08.45.55:915][868]LogShaderCompilers: Display: Cancelled job 0x00000A93DF7BC800 with pending SubmitJob call.
[2025.05.28-08.45.55:915][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFD200 with pending SubmitJob call.
[2025.05.28-08.45.55:915][868]LogShaderCompilers: Display: Cancelled job 0x00000A93A4DFE600 with pending SubmitJob call.
[2025.05.28-08.45.55:931][868]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.55:944][868]LogSkeletalMesh: Section 0: Material=0, 1004 triangles
[2025.05.28-08.45.55:944][868]LogSkeletalMesh: Building Skeletal Mesh saliva_lod0_mesh...
[2025.05.28-08.45.55:955][868]LogSkeletalMesh: Built Skeletal Mesh [0.01s] /Game/untitled_category/untitled_asset/saliva_lod0_mesh.saliva_lod0_mesh
[2025.05.28-08.45.55:956][868]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.05.28-08.45.55:965][868]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_14:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.55:965][868]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.55:965][868]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.55:965][868]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.05.28-08.45.55:973][868]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_15:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.55:973][868]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.55:973][868]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.55:990][868]LogUObjectHash: Compacting FUObjectHashTables data took   0.72ms
[2025.05.28-08.45.56:021][868]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.05.28-08.45.56:040][868]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.45.56:056][868]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.45.56:059][868]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.56:059][868]FBXImport: Warning: The bone size is too small to create Physics Asset 'saliva_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'saliva_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.56:083][869]LogUObjectHash: Compacting FUObjectHashTables data took   0.68ms
[2025.05.28-08.45.56:210][873]LogStreaming: Display: FlushAsyncLoading(568): 1 QueuedPackages, 0 AsyncPackages
[2025.05.28-08.45.56:210][873]LogStreaming: Warning: LoadPackage: SkipPackage: /Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset (0x466EF1832CA8EE25) - The package to load does not exist on disk or in the loader
[2025.05.28-08.45.56:210][873]LogUObjectGlobals: Warning: Failed to find object 'Object None./Game/untitled_category/untitled_asset/teeth_lod0_mesh_PhysicsAsset'
[2025.05.28-08.45.56:427][873]LogFactory: FactoryCreateFile: StaticMesh with FbxFactory (0 0 C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx)
[2025.05.28-08.45.56:433][873]LogFbx: Loading FBX Scene from C:/Users/<USER>/AppData/Local/Temp/blender/send2ue/data/SkeletalMesh/teeth_lod0_mesh.fbx
[2025.05.28-08.45.56:475][873]LogFbx: FBX Scene Loaded Succesfully
[2025.05.28-08.45.56:477][873]LogMaterial: Display: Material /Game/untitled_category/untitled_asset/MH_Friend_teeth_shader.MH_Friend_teeth_shader needed to have new flag set bUsedWithSkeletalMesh !
[2025.05.28-08.45.56:535][873]LogEditorFactories: Display: Image imported as : TSF BGRA8
[2025.05.28-08.45.56:657][873]LogFbx: Triangulating skeletal mesh teeth_lod0_mesh
[2025.05.28-08.45.56:770][873]LogFbx: Bones digested - 1  Depth of hierarchy - 1
[2025.05.28-08.45.56:897][873]LogSkeletalMesh: Section 0: Material=0, 8350 triangles
[2025.05.28-08.45.56:898][873]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.45.56:915][873]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.45.57:005][873]LogSkeletalMesh: Built Skeletal Mesh [0.11s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.45.57:006][873]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.05.28-08.45.57:013][873]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_16:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.57:013][873]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.57:014][873]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.57:014][873]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.05.28-08.45.57:021][873]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_17:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.05.28-08.45.57:021][873]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.45.57:021][873]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.45.57:039][873]LogUObjectHash: Compacting FUObjectHashTables data took   0.85ms
[2025.05.28-08.45.57:072][873]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.05.28-08.45.57:089][873]LogUObjectHash: Compacting FUObjectHashTables data took   0.81ms
[2025.05.28-08.45.57:106][873]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.05.28-08.45.57:153][873]LogSkeletalMesh: Building Skeletal Mesh teeth_lod0_mesh...
[2025.05.28-08.45.57:169][873]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (teeth_lod0_mesh) ...
[2025.05.28-08.45.57:330][873]LogSkeletalMesh: Built Skeletal Mesh [0.18s] /Game/untitled_category/untitled_asset/teeth_lod0_mesh.teeth_lod0_mesh
[2025.05.28-08.45.57:332][873]FBXImport: Warning: Could not find the bind pose.  It will use time 0 as bind pose. 
[2025.05.28-08.45.57:332][873]FBXImport: Warning: The bone size is too small to create Physics Asset 'teeth_lod0_mesh_PhysicsAsset' from Skeletal Mesh 'teeth_lod0_mesh'. You will have to create physics asset manually. 
[2025.05.28-08.45.57:357][874]LogUObjectHash: Compacting FUObjectHashTables data took   0.84ms
[2025.05.28-08.46.02:159][954]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.05.28-08.46.02:183][955]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.05.28-08.46.02:445][958]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.05.28-08.46.02:621][959]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.05.28-08.46.02:965][963]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.05.28-08.46.03:050][964]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_22
[2025.05.28-08.46.05:341][997]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.46.05:451][997]LogSlate: Window 'Message Log' being destroyed
[2025.05.28-08.46.05:522][998]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.46.05:813][ 15]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_23
[2025.05.28-08.46.05:832][ 16]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_24
[2025.05.28-08.46.05:859][ 17]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_25
[2025.05.28-08.46.05:882][ 18]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_26
[2025.05.28-08.46.05:940][ 21]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_27
[2025.05.28-08.46.05:961][ 22]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_28
[2025.05.28-08.46.05:986][ 23]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_29
[2025.05.28-08.46.06:011][ 24]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_30
[2025.05.28-08.46.06:036][ 25]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_31
[2025.05.28-08.46.06:060][ 26]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_32
[2025.05.28-08.46.06:086][ 27]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_33
[2025.05.28-08.46.06:110][ 28]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_34
[2025.05.28-08.46.06:136][ 29]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_35
[2025.05.28-08.46.06:163][ 30]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_36
[2025.05.28-08.46.15:527][840]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.05.28-08.46.16:949][956]LogUObjectHash: Compacting FUObjectHashTables data took   0.44ms
[2025.05.28-08.46.18:069][956]LogSlate: Window 'Save Content' being destroyed
[2025.05.28-08.46.18:155][956]LogStall: Shutdown...
[2025.05.28-08.46.18:155][956]LogStall: Shutdown complete.
[2025.05.28-08.46.18:209][956]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.05.28-08.46.18:266][956]Cmd: QUIT_EDITOR
[2025.05.28-08.46.18:266][957]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.05.28-08.46.18:272][957]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.05.28-08.46.18:273][957]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.05.28-08.46.18:273][957]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.05.28-08.46.18:281][957]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:281][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:281][957]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.05.28-08.46.18:295][957]LogStylusInput: Shutting down StylusInput subsystem.
[2025.05.28-08.46.18:295][957]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.05.28-08.46.18:297][957]LogWorld: UWorld::CleanupWorld for World_34, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:297][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:297][957]LogWorld: UWorld::CleanupWorld for World_33, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:297][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_32, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_31, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_30, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_29, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_28, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_27, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_26, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:298][957]LogWorld: UWorld::CleanupWorld for World_25, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:298][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_24, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_23, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_22, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_35, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:299][957]LogWorld: UWorld::CleanupWorld for World_36, bSessionEnded=true, bCleanupResources=true
[2025.05.28-08.46.18:299][957]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.05.28-08.46.18:300][957]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.05.28-08.46.18:306][957]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.05.28-08.46.18:306][957]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.05.28-08.46.18:306][957]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.05.28-08.46.18:309][957]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.05.28-08.46.18:309][957]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.05.28-08.46.18:309][957]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.05.28-08.46.18:309][957]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.05.28-08.46.18:309][957]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.46.18:312][957]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.05.28-08.46.18:318][957]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.05.28-08.46.18:318][957]LogAudio: Display: Audio Device Manager Shutdown
[2025.05.28-08.46.18:320][957]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.05.28-08.46.18:320][957]LogExit: Preparing to exit.
[2025.05.28-08.46.18:393][957]LogUObjectHash: Compacting FUObjectHashTables data took   0.74ms
[2025.05.28-08.46.19:410][957]LogEditorDataStorage: Deinitializing
[2025.05.28-08.46.20:043][957]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.05.28-08.46.20:053][957]LogExit: Editor shut down
[2025.05.28-08.46.20:056][957]LogExit: Transaction tracking system shut down
[2025.05.28-08.46.20:199][957]LogExit: Object subsystem successfully closed.
[2025.05.28-08.46.20:217][957]LogShaderCompilers: Display: Shaders left to compile 0
[2025.05.28-08.46.20:825][957]LogMemoryProfiler: Shutdown
[2025.05.28-08.46.20:825][957]LogNetworkingProfiler: Shutdown
[2025.05.28-08.46.20:825][957]LoadingProfiler: Shutdown
[2025.05.28-08.46.20:825][957]LogTimingProfiler: Shutdown
[2025.05.28-08.46.20:832][957]LogWebBrowser: Deleting browser for Url=file:///D:/UE_5.5/Engine/Plugins/Bridge/ThirdParty/megascans/index.html?token=eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ouG8aK1hZpvP4SNgAf_L6lmNzqpOPNUB9DMZ8AyN_SkXWKxX3dumAdTrHebYPjBd2cn3GUREeqJkKrDG0yMxJRRH3gblCRN-PO7PmtI1FuDRxnloHtmp-YXXSg-GDWpnZNPsrnbvHT-MSe1pwqU7UXWgldOHaKM9QoXfv6w8MUy_hlKdxKgWbrMc7E7q-mVzZMzpBu-S5g5ma6S_ywZW_m-6lYIJQQWGEFGD4nSwUGPQrdhcG4RKlBKXaMR4yGynjOwyS3An4QH7ZAAM81CJvkai5uOr10XUV3virRiNcLp8MRK_zBVVN5MvMvQhBVpdfIBjnBcEnUQiTQfvhcD5qA&refreshToken=NDA5YjczZDYtOTZhMi00MTg5LTgwN2ItYzEyNzIyYzgyYmJj#/.
[2025.05.28-08.46.20:832][957]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.46.20:832][957]LogBlenderLink: Closing listener socket
[2025.05.28-08.46.20:832][957]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.05.28-08.46.21:188][957]LogChaosDD: Chaos Debug Draw Shutdown
[2025.05.28-08.46.21:194][957]RenderDocPlugin: plugin has been unloaded.
[2025.05.28-08.46.21:230][957]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.05.28-08.46.21:230][957]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B1A3EABA6-41A5-BE65-CCAE-609B466677E7%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.05.28-08.46.22:782][957]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.05.28-08.46.22:782][957]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.05.28-08.46.22:782][957]LogNFORDenoise: NFORDenoise function shutting down
[2025.05.28-08.46.22:783][957]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.05.28-08.46.22:784][957]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.05.28-08.46.22:784][957]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.05.28-08.46.22:784][957]LogPakFile: Destroying PakPlatformFile
[2025.05.28-08.46.23:040][957]LogD3D12RHI: ~FD3D12DynamicRHI
[2025.05.28-08.46.23:099][957]LogExit: Exiting.
[2025.05.28-08.46.23:115][957]Log file closed, 05/28/25 14:16:23
